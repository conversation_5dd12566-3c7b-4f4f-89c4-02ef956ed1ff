// // Login screen will be defined here
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';

// import '../../core/theme/app_colors.dart';
// import '../../core/theme/app_typography.dart';
// import '../../core/widgets/app_button.dart';
// import '../../core/widgets/app_text_field.dart';
// // import 'otp_screen.dart';

// /// Login screen where user enters their phone number for OTP verification.
// class LoginScreen extends StatefulWidget {
//   const LoginScreen({super.key});

//   @override
//   State<LoginScreen> createState() => _LoginScreenState();
// }

// class _LoginScreenState extends State<LoginScreen> {
//   // Controller for phone number input
//   final TextEditingController _phoneController = TextEditingController();

//   // Validation flag for phone number
//   bool _isValidNumber = false;

//   /// Validates the phone number format (10 digits only).
//   void _validatePhone(String value) {
//     setState(() {
//       _isValidNumber = RegExp(r'^[0-9]{10}$').hasMatch(value);
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppColors.primary,
//       body: SafeArea(
//         child: SingleChildScrollView(
//           padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               const SizedBox(height: 60),

//               /// App logo
//               Image.asset('assets/icons/logo.png', height: 80, width: 80),

//               const SizedBox(height: 30),

//               /// Truck illustration
//               Image.asset('assets/icons/truck.png', height: 160, width: 200),

//               const SizedBox(height: 12),

//               /// Welcome title
//               const Text(
//                 "Welcome",
//                 textAlign: TextAlign.center,
//                 style: AppTypography.h1,
//               ),

//               const SizedBox(height: 30),

//               /// Instruction text
//               const Text(
//                 "Enter your phone number. We'll send an OTP to verify your number.",
//                 textAlign: TextAlign.center,
//                 style: AppTypography.body1,
//               ),

//               const SizedBox(height: 40),

//               /// Phone input field (reusable component)
//               AppTextField(
//                 controller: _phoneController,
//                 hintText: "Enter phone number",
//                 isValid: _isValidNumber,
//                 onChanged: _validatePhone,
//                 height: 55,
//                 width: 380,
//                 margin: const EdgeInsets.symmetric(vertical: 10),
//                 textStyle: AppTypography.input.copyWith(fontSize: 16),
//                 hintStyle: AppTypography.hint.copyWith(fontSize: 14),
//                 padding: const EdgeInsets.symmetric(
//                   horizontal: 12,
//                   vertical: 4,
//                 ),
//                 borderRadius: 12,
//                 keyboardType: TextInputType.phone,
//                 inputFormatters: [
//                   FilteringTextInputFormatter.digitsOnly,
//                   LengthLimitingTextInputFormatter(10),
//                 ],
//                 prefix: Row(
//                   mainAxisSize: MainAxisSize.min,
//                   children: const [
//                     Text("🇮🇳", style: TextStyle(fontSize: 20)),
//                     SizedBox(width: 10),
//                     Text("+91", style: AppTypography.body1),
//                     SizedBox(width: 10),
//                     VerticalDivider(
//                       width: 10,
//                       thickness: 1,
//                       color: AppColors.border,
//                     ),
//                     SizedBox(width: 10),
//                   ],
//                 ),
//               ),

//               const SizedBox(height: 25),

//               /// Continue button (enabled only if phone is valid)
//               AppButton(
//                 label: "Continue",
//                 textStyle: AppTypography.h3,
//                 isEnabled: _isValidNumber,
//                 onPressed: () {
//                   Navigator.push(
//                     context,
//                     MaterialPageRoute(
//                       builder: (context) => OtpVerificationScreen(
//                         phoneNumber: _phoneController.text,
//                       ),
//                     ),
//                   );
//                 },
//                 width: 350,
//                 height: 48,
//                 borderRadius: 10,
//               ),

//               const SizedBox(height: 20),

//               /// Terms & Privacy note
//               const Text(
//                 "By continuing, you agree to our Terms & Privacy Policy",
//                 textAlign: TextAlign.center,
//                 style: AppTypography.label,
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// /// OTP verification screen (navigated after phone number submission).
// class OtpVerificationScreen extends StatelessWidget {
//   final String phoneNumber;

//   const OtpVerificationScreen({super.key, required this.phoneNumber});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppColors.background,
//       body: Center(
//         child: Text("OTP for: $phoneNumber", style: AppTypography.body1),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/widgets/app_button.dart';
import '../../core/widgets/app_text_field.dart';
import 'otp_screen.dart'; // ✅ import OTP screen

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _phoneController = TextEditingController();
  bool _isValidNumber = false;

  void _validatePhone(String value) {
    setState(() {
      _isValidNumber = RegExp(r'^[0-9]{10}$').hasMatch(value);
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: screenHeight - keyboardHeight - 100,
              ),
              child: IntrinsicHeight(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: screenHeight > 700 ? 60 : 30),

                    /// App Logo
                    Image.asset(
                      'assets/icons/logo.png',
                      height: screenHeight > 700 ? 80 : 60,
                      width: screenHeight > 700 ? 80 : 60,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: screenHeight > 700 ? 80 : 60,
                        width: screenHeight > 700 ? 80 : 60,
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.business,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),

                    SizedBox(height: screenHeight > 700 ? 30 : 20),

                    /// Truck Illustration
                    Image.asset(
                      'assets/icons/truck.png',
                      height: screenHeight > 700 ? 160 : 120,
                      width: screenHeight > 700 ? 200 : 150,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: screenHeight > 700 ? 160 : 120,
                        width: screenHeight > 700 ? 200 : 150,
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.local_shipping,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),

                    SizedBox(height: screenHeight > 700 ? 20 : 15),

                    /// Welcome
                    const Text(
                      "Welcome",
                      style: AppTypography.h1,
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    /// Instruction
                    const Text(
                      "Enter your phone number. We'll send an OTP to verify your number.",
                      style: AppTypography.body1,
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: screenHeight > 700 ? 40 : 30),

                    /// Phone Input
                    AppTextField(
                      controller: _phoneController,
                      hintText: "Enter phone number",
                      isValid: _isValidNumber,
                      onChanged: _validatePhone,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(10),
                      ],
                      prefix: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: const [
                          Text("🇮🇳", style: TextStyle(fontSize: 20)),
                          SizedBox(width: 8),
                          Text(
                            "+91",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(width: 8),
                          VerticalDivider(
                            width: 10,
                            thickness: 1,
                            color: Colors.white38,
                          ),
                          SizedBox(width: 8),
                        ],
                      ),
                    ),

                    const SizedBox(height: 25),

                    /// Continue Button → Redirects to OTP screen
                    AppButton(
                      label: "Continue",
                      isEnabled: _isValidNumber,
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => OtpVerificationScreen(
                              phoneNumber: _phoneController.text,
                            ),
                          ),
                        );
                      },
                      width: double.infinity,
                      height: 48,
                      textStyle: AppTypography.input.copyWith(fontSize: 16),
                      borderRadius: 10,
                    ),

                    const Spacer(),

                    /// Terms
                    Padding(
                      padding: const EdgeInsets.only(bottom: 20),
                      child: Text(
                        "By continuing, you agree to our Terms & Privacy Policy",
                        style: AppTypography.label,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

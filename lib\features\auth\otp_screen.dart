import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/widgets/app_button.dart';
import '../registration/driver_register_screen.dart';

class OtpVerificationScreen extends StatefulWidget {
  final String phoneNumber;

  const OtpVerificationScreen({super.key, required this.phoneNumber});

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final List<TextEditingController> _otpControllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());

  Timer? _timer;
  int _resendTimer = 30;
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  void _startResendTimer() {
    _canResend = false;
    _resendTimer = 30;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_resendTimer > 0) {
          _resendTimer--;
        } else {
          _canResend = true;
          timer.cancel();
        }
      });
    });
  }

  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty && index < 5) {
      _focusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _focusNodes[index - 1].requestFocus();
    }
  }

  void _verifyOtp() {
    final otp = _otpControllers.map((c) => c.text).join();

    if (otp.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter all 6 digits'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) =>
            DriverRegisterScreen(phoneNumber: widget.phoneNumber),
      ),
    );
  }

  void _resendOtp() {
    if (_canResend) {
      for (var controller in _otpControllers) {
        controller.clear();
      }
      _focusNodes[0].requestFocus();
      _startResendTimer();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('OTP Resent Successfully!'),
          backgroundColor: Color.fromARGB(255, 26, 211, 5),
        ),
      );
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16),
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    screenHeight -
                    keyboardHeight -
                    100, // Account for SafeArea and padding
              ),
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    SizedBox(height: screenHeight > 700 ? 40 : 20),

                    /// Logo
                    Image.asset(
                      'assets/icons/logo.png',
                      height: screenHeight > 700 ? 80 : 60,
                      width: screenHeight > 700 ? 80 : 60,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: screenHeight > 700 ? 80 : 60,
                        width: screenHeight > 700 ? 80 : 60,
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.business,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                    SizedBox(height: screenHeight > 700 ? 12 : 8),

                    Text(
                      "NIKKOU",
                      style: AppTypography.h2.copyWith(
                        color: AppColors.textPrimary,
                        letterSpacing: 2,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      "LOGISTICS PVT LTD",
                      style: AppTypography.label.copyWith(
                        color: AppColors.textPrimary,
                        letterSpacing: 1,
                      ),
                    ),
                    SizedBox(height: screenHeight > 700 ? 30 : 20),

                    /// Truck
                    Image.asset(
                      'assets/icons/truck.png',
                      height: screenHeight > 700 ? 100 : 80,
                      width: screenHeight > 700 ? 160 : 128,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: screenHeight > 700 ? 100 : 80,
                        width: screenHeight > 700 ? 160 : 128,
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.local_shipping,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),

                    SizedBox(height: screenHeight > 700 ? 40 : 30),

                    /// Title
                    Text(
                      "Verify OTP",
                      style: AppTypography.h1.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),

                    /// Instruction
                    Text(
                      "Enter the 6-digit code\nsent to +91 ${widget.phoneNumber}",
                      style: AppTypography.body1.copyWith(
                        color: AppColors.textPrimary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: screenHeight > 700 ? 40 : 30),

                    /// OTP Fields
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: List.generate(6, (index) {
                          return Container(
                            width: 50,
                            height: 55,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppColors.border,
                                width: 1.5,
                              ),
                              color: AppColors.surface,
                            ),
                            child: TextField(
                              controller: _otpControllers[index],
                              focusNode: _focusNodes[index],
                              textAlign: TextAlign.center,
                              keyboardType: TextInputType.number,
                              maxLength: 1,
                              style: AppTypography.input.copyWith(
                                fontSize: 22,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                              ),
                              decoration: const InputDecoration(
                                counterText: "",
                                border: InputBorder.none,
                                hintText: "-",
                                hintStyle: TextStyle(
                                  color: AppColors.textTertiary,
                                  fontSize: 22,
                                ),
                              ),
                              inputFormatters: [
                                LengthLimitingTextInputFormatter(1),
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                              onChanged: (value) => _onOtpChanged(value, index),
                            ),
                          );
                        }),
                      ),
                    ),

                    SizedBox(height: screenHeight > 700 ? 30 : 20),

                    /// Resend OTP
                    GestureDetector(
                      onTap: _canResend ? _resendOtp : null,
                      child: Text(
                        _canResend
                            ? "Didn't receive? Resend OTP"
                            : "Resend in $_resendTimer s",
                        style: AppTypography.body2.copyWith(
                          color: _canResend
                              ? AppColors.secondary
                              : AppColors.textTertiary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),

                    SizedBox(height: screenHeight > 700 ? 40 : 30),

                    /// Verify Button
                    AppButton(
                      label: "Verify & Continue",
                      isEnabled: true,
                      onPressed: _verifyOtp,
                      width: double.infinity,
                      height: 50,
                      borderRadius: 10,
                    ),

                    const Spacer(),

                    /// Footer Links
                    Padding(
                      padding: const EdgeInsets.only(bottom: 20),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "Privacy Policy",
                                style: AppTypography.label.copyWith(
                                  color: AppColors.textPrimary,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                              const SizedBox(width: 6),
                              Text(
                                "•",
                                style: AppTypography.label.copyWith(
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              const SizedBox(width: 6),
                              Text(
                                "Terms",
                                style: AppTypography.label.copyWith(
                                  color: AppColors.textPrimary,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "Having trouble? Contact support",
                            style: AppTypography.label.copyWith(
                              color: AppColors.textPrimary,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

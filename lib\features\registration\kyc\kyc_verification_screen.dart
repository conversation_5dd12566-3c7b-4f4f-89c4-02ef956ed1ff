import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/widgets/app_card.dart';
import '../../home/<USER>';

class KYCVerificationScreen extends StatefulWidget {
  final String phoneNumber;
  final Map<String, String> driverData;

  const KYCVerificationScreen({
    super.key,
    required this.phoneNumber,
    required this.driverData,
  });

  @override
  State<KYCVerificationScreen> createState() => _KYCVerificationScreenState();
}

class _KYCVerificationScreenState extends State<KYCVerificationScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  VerificationStatus _status = VerificationStatus.pending;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _animationController.forward();
    _simulateVerification();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _simulateVerification() async {
    // Simulate verification process
    await Future.delayed(const Duration(seconds: 3));

    if (mounted) {
      setState(() {
        _status = VerificationStatus.approved;
      });

      // Auto-navigate to home after approval
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        _navigateToHome();
      }
    }
  }

  void _navigateToHome() {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const HomeScreen()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        title: Text(
          "KYC Verification",
          style: AppTypography.h2.copyWith(color: AppColors.textPrimary),
        ),
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Status Animation
            ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: _getStatusColor().withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getStatusIcon(),
                  size: 60,
                  color: _getStatusColor(),
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Status Title
            Text(
              _getStatusTitle(),
              style: AppTypography.h2.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Status Description
            Text(
              _getStatusDescription(),
              style: AppTypography.body1.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // Status Card
            AppCard(
              child: Column(
                children: [
                  _buildStatusItem("Personal Information", true),
                  _buildStatusItem("License Verification", true),
                  _buildStatusItem("Vehicle Documents", true),
                  _buildStatusItem(
                    "Background Check",
                    _status == VerificationStatus.approved,
                  ),
                  _buildStatusItem(
                    "Final Approval",
                    _status == VerificationStatus.approved,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 40),

            // Action Buttons
            if (_status == VerificationStatus.approved) ...[
              AppButton(
                label: "Continue to Dashboard",
                onPressed: _navigateToHome,
                width: double.infinity,
                height: 50,
              ),
            ] else if (_status == VerificationStatus.rejected) ...[
              AppButton(
                label: "Re-upload Documents",
                onPressed: () => Navigator.pop(context),
                width: double.infinity,
                height: 50,
              ),
              const SizedBox(height: 16),
              AppButton(
                label: "Contact Support",
                onPressed: _contactSupport,
                width: double.infinity,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(color: AppColors.secondary),
                ),
              ),
            ] else ...[
              // Pending state - show loading
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.secondary),
              ),
              const SizedBox(height: 16),
              Text(
                "Please wait while we verify your documents...",
                style: AppTypography.body2.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // Widget _buildSectionHeader(String title) {
  //   return Text(
  //     title,
  //     style: AppTypography.h3.copyWith(
  //       color: AppColors.textPrimary,
  //       fontWeight: FontWeight.bold,
  //     ),
  //   );
  // }

  Widget _buildStatusItem(String title, bool isCompleted) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isCompleted ? AppColors.success : AppColors.surface,
              shape: BoxShape.circle,
            ),
            child: Icon(
              isCompleted ? Icons.check : Icons.hourglass_empty,
              size: 16,
              color: isCompleted
                  ? AppColors.textPrimary
                  : AppColors.textSecondary,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: AppTypography.body1.copyWith(
                color: isCompleted
                    ? AppColors.textPrimary
                    : AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (_status) {
      case VerificationStatus.pending:
        return AppColors.warning;
      case VerificationStatus.approved:
        return AppColors.success;
      case VerificationStatus.rejected:
        return AppColors.error;
    }
  }

  IconData _getStatusIcon() {
    switch (_status) {
      case VerificationStatus.pending:
        return Icons.hourglass_empty;
      case VerificationStatus.approved:
        return Icons.check_circle;
      case VerificationStatus.rejected:
        return Icons.error;
    }
  }

  String _getStatusTitle() {
    switch (_status) {
      case VerificationStatus.pending:
        return "Verification in Progress";
      case VerificationStatus.approved:
        return "Verification Approved!";
      case VerificationStatus.rejected:
        return "Verification Failed";
    }
  }

  String _getStatusDescription() {
    switch (_status) {
      case VerificationStatus.pending:
        return "We are reviewing your documents and information. This usually takes 2-3 business days.";
      case VerificationStatus.approved:
        return "Congratulations! Your driver account has been approved. You can now start accepting orders.";
      case VerificationStatus.rejected:
        return "Some of your documents need to be re-uploaded. Please check the details and try again.";
    }
  }

  void _contactSupport() {
    // TODO: Implement support contact functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Support contact functionality will be implemented'),
        backgroundColor: AppColors.warning,
      ),
    );
  }
}

enum VerificationStatus { pending, approved, rejected }

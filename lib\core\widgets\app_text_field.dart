// App text field widget will be defined here
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

class AppTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final bool isValid;
  final void Function(String)? onChanged;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType keyboardType;
  final Widget? prefix;
  final Widget? suffix;
  final FocusNode? focusNode;
  final TextAlign textAlign;

  // Customizable props
  final double? width;
  final double height;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry padding;
  final double borderRadius;
  final BoxDecoration? decoration;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;

  const AppTextField({
    super.key,
    required this.controller,
    required this.hintText,
    this.isValid = true,
    this.onChanged,
    this.inputFormatters,
    this.keyboardType = TextInputType.text,
    this.prefix,
    this.suffix,
    this.focusNode,
    this.textAlign = TextAlign.start,
    this.width,
    this.height = 55,
    this.margin,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
    this.borderRadius = 10,
    this.decoration,
    this.textStyle,
    this.hintStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: width ?? double.infinity,
      height: height,
      padding: padding,
      decoration:
          decoration ??
          BoxDecoration(
            color: const Color.fromARGB(128, 129, 127, 127),
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: isValid
                  ? AppColors.success
                  : (controller.text.isEmpty
                        ? AppColors.border
                        : AppColors.error),
              width: 1.2,
            ),
          ),
      child: Row(
        children: [
          if (prefix != null) prefix!,
          Expanded(
            child: TextField(
              controller: controller,
              focusNode: focusNode,
              keyboardType: keyboardType,
              inputFormatters: inputFormatters,
              onChanged: onChanged,
              textAlign: textAlign,
              style: textStyle ?? AppTypography.input,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText,
                hintStyle: hintStyle ?? AppTypography.hint,
              ),
            ),
          ),
          if (suffix != null) suffix!,
        ],
      ),
    );
  }
}

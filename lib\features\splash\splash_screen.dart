import 'package:flutter/material.dart';
import '../auth/login_screen.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';

class SplashScreenConstants {
  // Durations
  static const Duration totalDuration = Duration(seconds: 8);

  // Sizes
  static const double truckSize = 200;
  static const double logoSize = 140;
  static const double mapWidthFactor = 0.9;

  // Asset paths
  static const String truckAssetPath = 'assets/icons/truck.png';
  static const String logoAssetPath = 'assets/icons/logo.png';
  static const String mapAssetPath = 'assets/icons/Map.png';
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  late Animation<double> _logoFade;
  late Animation<Offset> _logoSlide;

  late Animation<double> _welcomeFade;
  late Animation<Offset> _welcomeSlide;

  late Animation<double> _companyFade;
  late Animation<Offset> _companySlide;

  late Animation<double> _taglineFade;
  late Animation<Offset> _taglineSlide;

  late Animation<double> _mapFade;
  late Animation<Offset> _mapSlide;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: SplashScreenConstants.totalDuration,
    );

    _initAnimations();
    _controller.forward();

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed && mounted) {
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (_) => const LoginScreen()),
            );
          }
        });
      }
    });
  }

  void _initAnimations() {
    _logoFade = _fade(0.55, 0.65);
    _logoSlide = _slide(0.55, 0.65);

    _welcomeFade = _fade(0.66, 0.74);
    _welcomeSlide = _slide(0.66, 0.74);

    _companyFade = _fade(0.75, 0.83);
    _companySlide = _slide(0.75, 0.83);

    _taglineFade = _fade(0.84, 0.90);
    _taglineSlide = _slide(0.84, 0.90);

    _mapFade = _fade(0.91, 1.0);
    _mapSlide = _slide(0.91, 1.0);
  }

  Animation<double> _fade(double start, double end) {
    return Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(start, end, curve: Curves.easeIn),
      ),
    );
  }

  Animation<Offset> _slide(double start, double end) {
    return Tween<Offset>(begin: const Offset(0, 0.6), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(start, end, curve: Curves.easeOutBack),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildTruckAnimation(BuildContext context, double screenWidth) {
    final truckPosition =
        Tween<double>(
          begin: -SplashScreenConstants.truckSize,
          end: screenWidth + SplashScreenConstants.truckSize,
        ).animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
          ),
        );

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        if (_controller.value > 0.5) return const SizedBox.shrink();
        return Positioned(
          left: truckPosition.value,
          top:
              MediaQuery.of(context).size.height / 2 -
              SplashScreenConstants.truckSize / 2,
          child: Image.asset(
            SplashScreenConstants.truckAssetPath,
            width: SplashScreenConstants.truckSize,
            height: SplashScreenConstants.truckSize,
            errorBuilder: (c, e, s) =>
                _buildPlaceholder(SplashScreenConstants.truckSize),
          ),
        );
      },
    );
  }

  Widget _buildPlaceholder(double size) {
    return Container(
      width: size,
      height: size,
      color: AppColors.border,
      child: const Icon(Icons.error_outline, color: AppColors.textSecondary),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Stack(
          children: [
            _buildTruckAnimation(context, screenWidth),
            Center(
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: screenHeight - 100),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FadeTransition(
                        opacity: _logoFade,
                        child: SlideTransition(
                          position: _logoSlide,
                          child: Image.asset(
                            SplashScreenConstants.logoAssetPath,
                            width: screenHeight > 700
                                ? SplashScreenConstants.logoSize
                                : 100,
                            height: screenHeight > 700
                                ? SplashScreenConstants.logoSize
                                : 100,
                            errorBuilder: (c, e, s) => _buildPlaceholder(
                              screenHeight > 700
                                  ? SplashScreenConstants.logoSize
                                  : 100,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: screenHeight > 700 ? 20 : 15),

                      FadeTransition(
                        opacity: _welcomeFade,
                        child: SlideTransition(
                          position: _welcomeSlide,
                          child: Text(
                            "Welcome",
                            style: AppTypography.h1.copyWith(
                              fontSize: screenHeight > 700 ? null : 28,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      SizedBox(height: screenHeight > 700 ? 15 : 10),

                      FadeTransition(
                        opacity: _companyFade,
                        child: SlideTransition(
                          position: _companySlide,
                          child: Text(
                            "NIKKOU LOGISTICS",
                            style: AppTypography.h1.copyWith(
                              fontSize: screenHeight > 700 ? null : 24,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      SizedBox(height: screenHeight > 700 ? 10 : 8),

                      FadeTransition(
                        opacity: _taglineFade,
                        child: SlideTransition(
                          position: _taglineSlide,
                          child: const Text(
                            "Your logistics solution",
                            style: AppTypography.body1,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      SizedBox(height: screenHeight > 700 ? 40 : 30),

                      FadeTransition(
                        opacity: _mapFade,
                        child: SlideTransition(
                          position: _mapSlide,
                          child: Image.asset(
                            SplashScreenConstants.mapAssetPath,
                            width:
                                screenWidth *
                                (screenHeight > 700
                                    ? SplashScreenConstants.mapWidthFactor
                                    : 0.8),
                            errorBuilder: (c, e, s) => _buildPlaceholder(
                              screenWidth *
                                  (screenHeight > 700
                                      ? SplashScreenConstants.mapWidthFactor
                                      : 0.8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

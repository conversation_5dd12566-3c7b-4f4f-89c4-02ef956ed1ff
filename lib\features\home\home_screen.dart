import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        title: Text(
          "Driver Dashboard",
          style: AppTypography.h2.copyWith(color: AppColors.textPrimary),
        ),
        automaticallyImplyLeading: false,
      ),
      body: const Center(
        child: Text(
          "Welcome to Nikkou Driver Dashboard!\nThis screen will be implemented soon.",
          style: AppTypography.body1,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

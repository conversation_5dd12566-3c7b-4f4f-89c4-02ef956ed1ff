import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;

import '../models/driver_registration_model.dart';

class RegistrationService {
  static const String _baseUrl = 'https://api.nikkou.com'; // Replace with actual API URL
  
  // Submit driver registration data
  static Future<ApiResponse<String>> submitDriverRegistration(
    DriverRegistrationModel driverData,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/driver/register'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(driverData.toJson()),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return ApiResponse.success(data['registrationId'] ?? '');
      } else {
        final error = jsonDecode(response.body);
        return ApiResponse.error(error['message'] ?? 'Registration failed');
      }
    } catch (e) {
      return ApiResponse.error('Network error: ${e.toString()}');
    }
  }

  // Upload document
  static Future<ApiResponse<String>> uploadDocument(
    String registrationId,
    DocumentType documentType,
    File file,
  ) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/driver/upload-document'),
      );

      request.fields['registrationId'] = registrationId;
      request.fields['documentType'] = documentType.toString();
      
      request.files.add(
        await http.MultipartFile.fromPath(
          'document',
          file.path,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return ApiResponse.success(data['documentUrl'] ?? '');
      } else {
        final error = jsonDecode(response.body);
        return ApiResponse.error(error['message'] ?? 'Upload failed');
      }
    } catch (e) {
      return ApiResponse.error('Upload error: ${e.toString()}');
    }
  }

  // Check registration status
  static Future<ApiResponse<DriverRegistrationModel>> getRegistrationStatus(
    String phoneNumber,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/driver/status?phone=$phoneNumber'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final driverData = DriverRegistrationModel.fromJson(data);
        return ApiResponse.success(driverData);
      } else {
        final error = jsonDecode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to get status');
      }
    } catch (e) {
      return ApiResponse.error('Network error: ${e.toString()}');
    }
  }

  // Verify OTP for registration
  static Future<ApiResponse<bool>> verifyRegistrationOTP(
    String phoneNumber,
    String otp,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/driver/verify-otp'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'phoneNumber': phoneNumber,
          'otp': otp,
        }),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(true);
      } else {
        final error = jsonDecode(response.body);
        return ApiResponse.error(error['message'] ?? 'OTP verification failed');
      }
    } catch (e) {
      return ApiResponse.error('Network error: ${e.toString()}');
    }
  }

  // Resend OTP
  static Future<ApiResponse<bool>> resendOTP(String phoneNumber) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/driver/resend-otp'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'phoneNumber': phoneNumber,
        }),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(true);
      } else {
        final error = jsonDecode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to resend OTP');
      }
    } catch (e) {
      return ApiResponse.error('Network error: ${e.toString()}');
    }
  }

  // Update registration data
  static Future<ApiResponse<bool>> updateRegistrationData(
    String registrationId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final response = await http.patch(
        Uri.parse('$_baseUrl/driver/update/$registrationId'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(updates),
      );

      if (response.statusCode == 200) {
        return ApiResponse.success(true);
      } else {
        final error = jsonDecode(response.body);
        return ApiResponse.error(error['message'] ?? 'Update failed');
      }
    } catch (e) {
      return ApiResponse.error('Network error: ${e.toString()}');
    }
  }

  // Get list of required documents
  static Future<ApiResponse<List<DocumentRequirement>>> getDocumentRequirements() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/driver/document-requirements'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<DocumentRequirement> requirements = (data['requirements'] as List)
            .map((item) => DocumentRequirement.fromJson(item))
            .toList();
        return ApiResponse.success(requirements);
      } else {
        final error = jsonDecode(response.body);
        return ApiResponse.error(error['message'] ?? 'Failed to get requirements');
      }
    } catch (e) {
      return ApiResponse.error('Network error: ${e.toString()}');
    }
  }
}

// Generic API response wrapper
class ApiResponse<T> {
  final bool isSuccess;
  final T? data;
  final String? error;

  const ApiResponse._({
    required this.isSuccess,
    this.data,
    this.error,
  });

  factory ApiResponse.success(T data) {
    return ApiResponse._(
      isSuccess: true,
      data: data,
    );
  }

  factory ApiResponse.error(String error) {
    return ApiResponse._(
      isSuccess: false,
      error: error,
    );
  }
}

// Document requirement model
class DocumentRequirement {
  final DocumentType type;
  final String title;
  final String description;
  final bool isRequired;
  final List<String> acceptedFormats;
  final int maxSizeMB;

  const DocumentRequirement({
    required this.type,
    required this.title,
    required this.description,
    required this.isRequired,
    required this.acceptedFormats,
    required this.maxSizeMB,
  });

  factory DocumentRequirement.fromJson(Map<String, dynamic> json) {
    return DocumentRequirement(
      type: DocumentType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => DocumentType.profilePhoto,
      ),
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      isRequired: json['isRequired'] ?? true,
      acceptedFormats: List<String>.from(json['acceptedFormats'] ?? ['jpg', 'png']),
      maxSizeMB: json['maxSizeMB'] ?? 5,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'title': title,
      'description': description,
      'isRequired': isRequired,
      'acceptedFormats': acceptedFormats,
      'maxSizeMB': maxSizeMB,
    };
  }
}

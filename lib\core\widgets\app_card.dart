// // App card widget will be defined here
// import 'package:flutter/material.dart';
// import '../theme/app_colors.dart';

// class AppCard extends StatelessWidget {
//   final Widget child;

//   // Customizable
//   final double? width;
//   final double? height;
//   final EdgeInsetsGeometry padding;
//   final EdgeInsetsGeometry margin;
//   final double borderRadius;
//   final Color backgroundColor;
//   final BoxBorder? border;
//   final List<BoxShadow>? boxShadow;

//   const AppCard({
//     super.key,
//     required this.child,
//     this.width,
//     this.height,
//     this.padding = const EdgeInsets.all(16),
//     this.margin = const EdgeInsets.all(8),
//     this.borderRadius = 16,
//     this.backgroundColor = AppColors.surface,
//     this.border,
//     this.boxShadow,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       width: width,
//       height: height,
//       margin: margin,
//       padding: padding,
//       decoration: BoxDecoration(
//         color: backgroundColor,
//         borderRadius: BorderRadius.circular(borderRadius),
//         border: border,
//         boxShadow:
//             boxShadow ??
//             [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.1),
//                 blurRadius: 8,
//                 offset: const Offset(0, 4),
//               ),
//             ],
//       ),
//       child: child,
//     );
//   }
// }

import 'package:flutter/material.dart';
import '../theme/app_colors.dart';

class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;

  const AppCard({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(20),
    this.margin = const EdgeInsets.only(bottom: 16),
    this.borderRadius = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 242, 239, 239),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.background.withOpacity(0.5),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: child,
    );
  }
}

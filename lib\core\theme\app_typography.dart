import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Centralized text styles (Typography system).
/// Uses Poppins/Roboto as example fonts. Replace with your chosen font family.
class AppTypography {
  static const String primaryFont = "Poppins";
  static const String secondaryFont = "Roboto";

  // Headings
  static const TextStyle h1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
  );

  static const TextStyle h2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
  );

  static const TextStyle h3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w500,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
  );

  // Body
  static const TextStyle body1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    fontFamily: secondaryFont,
    color: AppColors.textSecondary,
  );

  static const TextStyle body2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    fontFamily: secondaryFont,
    color: AppColors.textTertiary,
  );

  // Labels
  static const TextStyle label = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    fontFamily: secondaryFont,
    color: AppColors.textTertiary,
  );

  // Buttons
  static const TextStyle button = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 1.2,
  );

  // Inputs
  static const TextStyle input = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    fontFamily: secondaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 1.2,
  );

  // Hints
  static const TextStyle hint = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    fontFamily: secondaryFont,
    color: AppColors.textTertiary,
  );
}

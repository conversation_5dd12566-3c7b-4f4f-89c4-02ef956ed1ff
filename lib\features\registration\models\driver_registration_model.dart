import 'package:flutter/material.dart';
import 'package:nikkou_driver_app/core/theme/app_colors.dart';

class DriverRegistrationModel {
  // Personal Information
  final String fullName;
  final String email;
  final String phoneNumber;
  final String dateOfBirth;
  final String address;
  final String city;
  final String state;
  final String pinCode;

  // License Information
  final String licenseNumber;
  final String licenseExpiry;
  final String licenseClass;

  // Vehicle Information
  final String vehicleNumber;
  final String vehicleType;
  final String vehicleModel;
  final String vehicleYear;
  final String vehicleColor;

  // Emergency Contact
  final String emergencyContactName;
  final String emergencyContactPhone;
  final String emergencyContactRelation;

  // KYC Documents
  final Map<String, String> documentPaths;

  // Registration Status
  final RegistrationStatus status;
  final DateTime createdAt;
  final DateTime? approvedAt;
  final String? rejectionReason;

  const DriverRegistrationModel({
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    required this.dateOfBirth,
    required this.address,
    required this.city,
    required this.state,
    required this.pinCode,
    required this.licenseNumber,
    required this.licenseExpiry,
    required this.licenseClass,
    required this.vehicleNumber,
    required this.vehicleType,
    required this.vehicleModel,
    required this.vehicleYear,
    required this.vehicleColor,
    required this.emergencyContactName,
    required this.emergencyContactPhone,
    required this.emergencyContactRelation,
    required this.documentPaths,
    required this.status,
    required this.createdAt,
    this.approvedAt,
    this.rejectionReason,
  });

  // Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'email': email,
      'phoneNumber': phoneNumber,
      'dateOfBirth': dateOfBirth,
      'address': address,
      'city': city,
      'state': state,
      'pinCode': pinCode,
      'licenseNumber': licenseNumber,
      'licenseExpiry': licenseExpiry,
      'licenseClass': licenseClass,
      'vehicleNumber': vehicleNumber,
      'vehicleType': vehicleType,
      'vehicleModel': vehicleModel,
      'vehicleYear': vehicleYear,
      'vehicleColor': vehicleColor,
      'emergencyContactName': emergencyContactName,
      'emergencyContactPhone': emergencyContactPhone,
      'emergencyContactRelation': emergencyContactRelation,
      'documentPaths': documentPaths,
      'status': status.toString(),
      'createdAt': createdAt.toIso8601String(),
      'approvedAt': approvedAt?.toIso8601String(),
      'rejectionReason': rejectionReason,
    };
  }

  // Create from JSON response
  factory DriverRegistrationModel.fromJson(Map<String, dynamic> json) {
    return DriverRegistrationModel(
      fullName: json['fullName'] ?? '',
      email: json['email'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      dateOfBirth: json['dateOfBirth'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      pinCode: json['pinCode'] ?? '',
      licenseNumber: json['licenseNumber'] ?? '',
      licenseExpiry: json['licenseExpiry'] ?? '',
      licenseClass: json['licenseClass'] ?? '',
      vehicleNumber: json['vehicleNumber'] ?? '',
      vehicleType: json['vehicleType'] ?? '',
      vehicleModel: json['vehicleModel'] ?? '',
      vehicleYear: json['vehicleYear'] ?? '',
      vehicleColor: json['vehicleColor'] ?? '',
      emergencyContactName: json['emergencyContactName'] ?? '',
      emergencyContactPhone: json['emergencyContactPhone'] ?? '',
      emergencyContactRelation: json['emergencyContactRelation'] ?? '',
      documentPaths: Map<String, String>.from(json['documentPaths'] ?? {}),
      status: RegistrationStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => RegistrationStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      approvedAt: json['approvedAt'] != null
          ? DateTime.parse(json['approvedAt'])
          : null,
      rejectionReason: json['rejectionReason'],
    );
  }

  // Copy with method for updates
  DriverRegistrationModel copyWith({
    String? fullName,
    String? email,
    String? phoneNumber,
    String? dateOfBirth,
    String? address,
    String? city,
    String? state,
    String? pinCode,
    String? licenseNumber,
    String? licenseExpiry,
    String? licenseClass,
    String? vehicleNumber,
    String? vehicleType,
    String? vehicleModel,
    String? vehicleYear,
    String? vehicleColor,
    String? emergencyContactName,
    String? emergencyContactPhone,
    String? emergencyContactRelation,
    Map<String, String>? documentPaths,
    RegistrationStatus? status,
    DateTime? createdAt,
    DateTime? approvedAt,
    String? rejectionReason,
  }) {
    return DriverRegistrationModel(
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      pinCode: pinCode ?? this.pinCode,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      licenseExpiry: licenseExpiry ?? this.licenseExpiry,
      licenseClass: licenseClass ?? this.licenseClass,
      vehicleNumber: vehicleNumber ?? this.vehicleNumber,
      vehicleType: vehicleType ?? this.vehicleType,
      vehicleModel: vehicleModel ?? this.vehicleModel,
      vehicleYear: vehicleYear ?? this.vehicleYear,
      vehicleColor: vehicleColor ?? this.vehicleColor,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone:
          emergencyContactPhone ?? this.emergencyContactPhone,
      emergencyContactRelation:
          emergencyContactRelation ?? this.emergencyContactRelation,
      documentPaths: documentPaths ?? this.documentPaths,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      approvedAt: approvedAt ?? this.approvedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
    );
  }
}

enum RegistrationStatus {
  pending,
  documentsUploaded,
  underReview,
  approved,
  rejected,
  suspended,
}

extension RegistrationStatusExtension on RegistrationStatus {
  String get displayName {
    switch (this) {
      case RegistrationStatus.pending:
        return 'Pending';
      case RegistrationStatus.documentsUploaded:
        return 'Documents Uploaded';
      case RegistrationStatus.underReview:
        return 'Under Review';
      case RegistrationStatus.approved:
        return 'Approved';
      case RegistrationStatus.rejected:
        return 'Rejected';
      case RegistrationStatus.suspended:
        return 'Suspended';
    }
  }

  Color get statusColor {
    switch (this) {
      case RegistrationStatus.pending:
      case RegistrationStatus.documentsUploaded:
      case RegistrationStatus.underReview:
        return AppColors.warning;
      case RegistrationStatus.approved:
        return AppColors.success;
      case RegistrationStatus.rejected:
      case RegistrationStatus.suspended:
        return AppColors.error;
    }
  }

  IconData get statusIcon {
    switch (this) {
      case RegistrationStatus.pending:
      case RegistrationStatus.documentsUploaded:
      case RegistrationStatus.underReview:
        return Icons.hourglass_empty;
      case RegistrationStatus.approved:
        return Icons.check_circle;
      case RegistrationStatus.rejected:
      case RegistrationStatus.suspended:
        return Icons.error;
    }
  }
}

// Document types for KYC
enum DocumentType {
  profilePhoto,
  aadharFront,
  aadharBack,
  panCard,
  drivingLicense,
  vehicleRC,
  vehicleInsurance,
  vehiclePhoto,
}

extension DocumentTypeExtension on DocumentType {
  String get displayName {
    switch (this) {
      case DocumentType.profilePhoto:
        return 'Profile Photo';
      case DocumentType.aadharFront:
        return 'Aadhar Card (Front)';
      case DocumentType.aadharBack:
        return 'Aadhar Card (Back)';
      case DocumentType.panCard:
        return 'PAN Card';
      case DocumentType.drivingLicense:
        return 'Driving License';
      case DocumentType.vehicleRC:
        return 'Vehicle RC';
      case DocumentType.vehicleInsurance:
        return 'Vehicle Insurance';
      case DocumentType.vehiclePhoto:
        return 'Vehicle Photo';
    }
  }

  String get description {
    switch (this) {
      case DocumentType.profilePhoto:
        return 'Clear photo of your face';
      case DocumentType.aadharFront:
        return 'Front side of Aadhar card';
      case DocumentType.aadharBack:
        return 'Back side of Aadhar card';
      case DocumentType.panCard:
        return 'PAN card for tax purposes';
      case DocumentType.drivingLicense:
        return 'Valid driving license';
      case DocumentType.vehicleRC:
        return 'Registration certificate';
      case DocumentType.vehicleInsurance:
        return 'Valid insurance certificate';
      case DocumentType.vehiclePhoto:
        return 'Clear photo of your vehicle';
    }
  }

  bool get isRequired {
    return true; // All documents are required for driver registration
  }
}

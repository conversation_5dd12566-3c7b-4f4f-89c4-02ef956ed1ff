import 'package:flutter/material.dart';
import 'dart:io';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_typography.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/widgets/app_card.dart';
import 'kyc_verification_screen.dart';

class KYCDocumentsScreen extends StatefulWidget {
  final String phoneNumber;
  final Map<String, String> driverData;

  const KYCDocumentsScreen({
    super.key,
    required this.phoneNumber,
    required this.driverData,
  });

  @override
  State<KYCDocumentsScreen> createState() => _KYCDocumentsScreenState();
}

class _KYCDocumentsScreenState extends State<KYCDocumentsScreen> {
  // Document upload states
  File? _profilePhoto;
  File? _licensePhoto;
  File? _aadharFront;
  File? _aadharBack;
  File? _panCard;
  File? _vehicleRC;
  File? _vehicleInsurance;
  File? _vehiclePhoto;

  // Upload progress states
  bool _isUploading = false;
  double _uploadProgress = 0.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        title: Text(
          "KYC Documents",
          style: AppTypography.h2.copyWith(color: AppColors.textPrimary),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Instructions
            AppCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Document Upload Instructions",
                    style: AppTypography.h3.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    "• Ensure all documents are clear and readable\n"
                    "• Upload original documents only\n"
                    "• File size should be less than 5MB\n"
                    "• Supported formats: JPG, PNG, PDF",
                    style: AppTypography.body2.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Personal Documents Section
            _buildSectionHeader("Personal Documents"),
            const SizedBox(height: 16),
            
            _buildDocumentUploadCard(
              title: "Profile Photo",
              subtitle: "Clear photo of your face",
              file: _profilePhoto,
              onTap: () => _pickDocument('profile'),
              isRequired: true,
            ),
            
            _buildDocumentUploadCard(
              title: "Aadhar Card (Front)",
              subtitle: "Front side of Aadhar card",
              file: _aadharFront,
              onTap: () => _pickDocument('aadhar_front'),
              isRequired: true,
            ),
            
            _buildDocumentUploadCard(
              title: "Aadhar Card (Back)",
              subtitle: "Back side of Aadhar card",
              file: _aadharBack,
              onTap: () => _pickDocument('aadhar_back'),
              isRequired: true,
            ),
            
            _buildDocumentUploadCard(
              title: "PAN Card",
              subtitle: "PAN card for tax purposes",
              file: _panCard,
              onTap: () => _pickDocument('pan'),
              isRequired: true,
            ),
            
            const SizedBox(height: 24),
            
            // License Documents Section
            _buildSectionHeader("License Documents"),
            const SizedBox(height: 16),
            
            _buildDocumentUploadCard(
              title: "Driving License",
              subtitle: "Valid driving license",
              file: _licensePhoto,
              onTap: () => _pickDocument('license'),
              isRequired: true,
            ),
            
            const SizedBox(height: 24),
            
            // Vehicle Documents Section
            _buildSectionHeader("Vehicle Documents"),
            const SizedBox(height: 16),
            
            _buildDocumentUploadCard(
              title: "Vehicle RC",
              subtitle: "Registration certificate",
              file: _vehicleRC,
              onTap: () => _pickDocument('vehicle_rc'),
              isRequired: true,
            ),
            
            _buildDocumentUploadCard(
              title: "Vehicle Insurance",
              subtitle: "Valid insurance certificate",
              file: _vehicleInsurance,
              onTap: () => _pickDocument('vehicle_insurance'),
              isRequired: true,
            ),
            
            _buildDocumentUploadCard(
              title: "Vehicle Photo",
              subtitle: "Clear photo of your vehicle",
              file: _vehiclePhoto,
              onTap: () => _pickDocument('vehicle_photo'),
              isRequired: true,
            ),
            
            const SizedBox(height: 40),
            
            // Upload Progress
            if (_isUploading) ...[
              LinearProgressIndicator(
                value: _uploadProgress,
                backgroundColor: AppColors.surface,
                valueColor: const AlwaysStoppedAnimation<Color>(AppColors.secondary),
              ),
              const SizedBox(height: 16),
              Text(
                "Uploading documents... ${(_uploadProgress * 100).toInt()}%",
                style: AppTypography.body2.copyWith(color: AppColors.textSecondary),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
            ],
            
            // Submit Button
            AppButton(
              label: "Submit for Verification",
              onPressed: _allDocumentsUploaded() && !_isUploading ? _submitDocuments : null,
              isEnabled: _allDocumentsUploaded() && !_isUploading,
              width: double.infinity,
              height: 50,
            ),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: AppTypography.h3.copyWith(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildDocumentUploadCard({
    required String title,
    required String subtitle,
    required File? file,
    required VoidCallback onTap,
    required bool isRequired,
  }) {
    final bool isUploaded = file != null;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: AppCard(
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: isUploaded ? AppColors.success : AppColors.surface,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Icon(
                    isUploaded ? Icons.check : Icons.upload_file,
                    color: isUploaded ? AppColors.textPrimary : AppColors.textSecondary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            title,
                            style: AppTypography.body1.copyWith(
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (isRequired) ...[
                            const SizedBox(width: 4),
                            Text(
                              "*",
                              style: AppTypography.body1.copyWith(
                                color: AppColors.error,
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isUploaded ? "Document uploaded" : subtitle,
                        style: AppTypography.body2.copyWith(
                          color: isUploaded ? AppColors.success : AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _allDocumentsUploaded() {
    return _profilePhoto != null &&
        _aadharFront != null &&
        _aadharBack != null &&
        _panCard != null &&
        _licensePhoto != null &&
        _vehicleRC != null &&
        _vehicleInsurance != null &&
        _vehiclePhoto != null;
  }

  void _pickDocument(String documentType) {
    // TODO: Implement image picker functionality
    // For now, we'll simulate document selection
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Document picker for $documentType will be implemented'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _submitDocuments() {
    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    // Simulate upload progress
    _simulateUpload();
  }

  void _simulateUpload() async {
    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(const Duration(milliseconds: 200));
      setState(() {
        _uploadProgress = i / 100;
      });
    }

    setState(() {
      _isUploading = false;
    });

    // Navigate to verification screen
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => KYCVerificationScreen(
          phoneNumber: widget.phoneNumber,
          driverData: widget.driverData,
        ),
      ),
    );
  }
}

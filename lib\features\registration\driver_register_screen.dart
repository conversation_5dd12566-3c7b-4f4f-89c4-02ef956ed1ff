import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/widgets/app_button.dart';
import '../../core/widgets/app_text_field.dart';
import '../../core/widgets/app_dropdown.dart';

import 'kyc/kyc_documents_screen.dart';

class DriverRegisterScreen extends StatefulWidget {
  final String phoneNumber;

  const DriverRegisterScreen({super.key, required this.phoneNumber});

  @override
  State<DriverRegisterScreen> createState() => _DriverRegisterScreenState();
}

class _DriverRegisterScreenState extends State<DriverRegisterScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _fullNameController = TextEditingController();
  final _ageController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _cityController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _currentAddressController = TextEditingController();
  final _residentialAddressController = TextEditingController();
  final _emergencyContactNameController = TextEditingController();
  final _emergencyContactPhoneController = TextEditingController();

  // Dropdowns
  String? _selectedGender;
  String? _selectedCountry;
  String? _selectedState;
  bool _sameAsCurrentAddress = false;

  // Profile photo
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();

  final List<String> _genderOptions = ["Male", "Female", "Other"];
  final List<String> _countries = ["Select Country", "India", "USA", "UK"];
  final List<String> _states = [
    "Select State",
    "Telangana",
    "Andhra Pradesh",
    "Karnataka",
  ];

  @override
  void initState() {
    super.initState();
    _phoneController.text = widget.phoneNumber;
  }

  Future<void> _pickImage() async {
    final XFile? image = await _picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 85,
    );
    if (image != null) {
      setState(() => _profileImage = File(image.path));
    }
  }

  void _submitRegistration() {
    if (_formKey.currentState!.validate()) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => KYCDocumentsScreen(
            phoneNumber: _phoneController.text,
            driverData: {
              "fullName": _fullNameController.text.trim(),
              "age": _ageController.text.trim(),
              "phone": _phoneController.text.trim(),
              "email": _emailController.text.trim(),
              "gender": _selectedGender ?? "",
              "country": _selectedCountry ?? "",
              "state": _selectedState ?? "",
              "city": _cityController.text.trim(),
              "pincode": _pincodeController.text.trim(),
              "currentAddress": _currentAddressController.text.trim(),
              "residentialAddress": _residentialAddressController.text.trim(),
              "emergencyContactName": _emergencyContactNameController.text
                  .trim(),
              "emergencyContactPhone": _emergencyContactPhoneController.text
                  .trim(),
            },
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Main Content
                Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Complete Your Profile Section
                        _buildProfileSection(),

                        const SizedBox(height: 24),

                        // Personal Information Section
                        _buildPersonalInfoSection(),

                        const SizedBox(height: 24),

                        // Location Details Section
                        _buildLocationSection(),

                        const SizedBox(height: 24),

                        // Address Information Section
                        _buildAddressSection(),

                        const SizedBox(height: 24),

                        // Emergency Contact Section
                        _buildEmergencyContactSection(),

                        const SizedBox(height: 32),

                        // Save Profile Button
                        AppButton(
                          label: "Save Profile",
                          onPressed: _submitRegistration,
                          width: double.infinity,
                          height: 50,
                          borderRadius: 10,
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const SizedBox(height: 20),
          Image.asset(
            "assets/icons/logo.png",
            height: 50,
            errorBuilder: (context, error, stackTrace) => Container(
              height: 50,
              width: 50,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.business, color: AppColors.textSecondary),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "NIKKOU",
            style: AppTypography.h2.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            "Driver Registration",
            style: AppTypography.body1.copyWith(
              color: AppColors.textPrimary,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Complete Your Profile",
          style: AppTypography.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Profile Photo Upload
        Center(
          child: GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.border, width: 2),
              ),
              child: _profileImage != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Image.file(
                        _profileImage!,
                        width: 100,
                        height: 100,
                        fit: BoxFit.cover,
                      ),
                    )
                  : const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.camera_alt,
                          size: 30,
                          color: AppColors.textSecondary,
                        ),
                        SizedBox(height: 4),
                        Text(
                          "Upload",
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Center(
          child: Text(
            "Upload your profile photo (Max 5MB)",
            style: AppTypography.body2.copyWith(color: AppColors.textSecondary),
          ),
        ),
        const SizedBox(height: 16),

        // Upload Photo Button
        Center(
          child: AppButton(
            label: "Upload Photo",
            onPressed: _pickImage,
            width: 150,
            height: 40,
            borderRadius: 8,
            textStyle: AppTypography.body2.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Full Name*",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _fullNameController,
          hintText: "Enter your full name",
        ),

        const SizedBox(height: 16),

        Text(
          "Age*",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: AppTextField(
                controller: _ageController,
                hintText: "Enter your age",
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Gender*",
                    style: AppTypography.body1.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  AppDropdown(
                    hint: "Select Gender",
                    value: _selectedGender,
                    items: _genderOptions,
                    onChanged: (val) => setState(() => _selectedGender = val),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        Text(
          "Phone Number*",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _phoneController,
          hintText: "+91 98765 43210",
          keyboardType: TextInputType.phone,
        ),

        const SizedBox(height: 16),

        Text(
          "Email",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _emailController,
          hintText: "<EMAIL>",
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Location Details",
          style: AppTypography.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        Text(
          "Country",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppDropdown(
          hint: "Select Country",
          value: _selectedCountry,
          items: _countries,
          onChanged: (val) => setState(() => _selectedCountry = val),
        ),

        const SizedBox(height: 16),

        Text(
          "State",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppDropdown(
          hint: "Select State",
          value: _selectedState,
          items: _states,
          onChanged: (val) => setState(() => _selectedState = val),
        ),

        const SizedBox(height: 16),

        Text(
          "City",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppTextField(controller: _cityController, hintText: "Mumbai"),

        const SizedBox(height: 16),

        Text(
          "Pin",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _pincodeController,
          hintText: "400001",
          keyboardType: TextInputType.number,
        ),
      ],
    );
  }

  Widget _buildAddressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Address Information",
          style: AppTypography.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        Row(
          children: [
            Checkbox(
              value: _sameAsCurrentAddress,
              activeColor: AppColors.secondary,
              onChanged: (val) =>
                  setState(() => _sameAsCurrentAddress = val ?? false),
            ),
            Expanded(
              child: Text(
                "Same as Current Address",
                style: AppTypography.body2.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        Text(
          "Same as Residential Address",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _currentAddressController,
          hintText: "Enter current address",
        ),

        const SizedBox(height: 16),

        if (!_sameAsCurrentAddress) ...[
          Text(
            "Residential Address",
            style: AppTypography.body1.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          AppTextField(
            controller: _residentialAddressController,
            hintText: "Enter residential address",
          ),
        ],
      ],
    );
  }

  Widget _buildEmergencyContactSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Emergency Contact",
          style: AppTypography.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        Text(
          "Contact Name",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _emergencyContactNameController,
          hintText: "John Doe",
        ),

        const SizedBox(height: 16),

        Text(
          "Emergency Contact Phone",
          style: AppTypography.body1.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        AppTextField(
          controller: _emergencyContactPhoneController,
          hintText: "+91 98765 43210",
          keyboardType: TextInputType.phone,
        ),
      ],
    );
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _ageController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _cityController.dispose();
    _pincodeController.dispose();
    _currentAddressController.dispose();
    _residentialAddressController.dispose();
    _emergencyContactNameController.dispose();
    _emergencyContactPhoneController.dispose();
    super.dispose();
  }
}
